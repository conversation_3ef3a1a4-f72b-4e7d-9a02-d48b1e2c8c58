<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // For now, return with empty data - you can add actual database queries later
        $courses = collect(); // Empty collection for now
        $categories = collect(); // Empty collection for now
        $totalCourses = 0;
        $publishedCourses = 0;
        $draftCourses = 0;
        $featuredCourses = 0;

        return view('admin.courses.index', compact(
            'courses',
            'categories',
            'totalCourses',
            'publishedCourses',
            'draftCourses',
            'featuredCourses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // For now, return with empty data - you can add actual database queries later
        $categories = collect(); // Empty collection for now
        $instructors = collect(); // Empty collection for now

        return view('admin.courses.create', compact('categories', 'instructors'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug',
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            // Generate slug if not provided
            $slug = $request->slug ?: Str::slug($request->title);
            
            // Handle thumbnail upload
            $thumbnailPath = null;
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $request->file('thumbnail')->store('courses', 'public');
            }

            // Validate pricing logic
            if (!$request->has('is_free')) {
                if ($request->discount_price && $request->discount_price >= $request->price) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'Discount price must be less than regular price.');
                }
            }

            // Here you would typically save to database
            // Example:
            // Course::create([
            //     'title' => $request->title,
            //     'slug' => $slug,
            //     'short_description' => $request->short_description,
            //     'description' => $request->description,
            //     'learning_objectives' => $request->learning_objectives,
            //     'requirements' => $request->requirements,
            //     'category_id' => $request->category_id,
            //     'instructor_id' => $request->instructor_id,
            //     'status' => $request->status,
            //     'duration' => $request->duration,
            //     'difficulty_level' => $request->difficulty_level,
            //     'language' => $request->language,
            //     'certificate' => $request->has('certificate'),
            //     'is_featured' => $request->has('is_featured'),
            //     'is_free' => $request->has('is_free'),
            //     'price' => $request->has('is_free') ? 0 : $request->price,
            //     'discount_price' => $request->has('is_free') ? 0 : $request->discount_price,
            //     'thumbnail' => $thumbnailPath,
            //     'preview_video' => $request->preview_video,
            //     'meta_title' => $request->meta_title,
            //     'meta_description' => $request->meta_description,
            //     'tags' => $request->tags,
            // ]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error creating course: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // For now, redirect to edit
        return redirect()->route('admin.courses.edit', $id);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // For now, return a simple view - you can add actual database queries later
        $categories = collect(); // Empty collection for now
        $instructors = collect(); // Empty collection for now
        
        return view('admin.courses.edit', compact('id', 'categories', 'instructors'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug,' . $id,
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            // Here you would typically update the database record
            // Example:
            // $course = Course::findOrFail($id);
            // $course->update([...]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating course: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // Here you would typically delete from database
            // Example:
            // $course = Course::findOrFail($id);
            // $course->delete();

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error deleting course: ' . $e->getMessage());
        }
    }

    /**
     * Handle bulk actions for courses.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,draft,delete',
            'course_ids' => 'required|array',
            'course_ids.*' => 'integer',
        ]);

        try {
            $action = $request->action;
            $courseIds = $request->course_ids;
            $count = count($courseIds);

            // Here you would typically perform bulk operations on database
            // Example:
            // switch ($action) {
            //     case 'publish':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'published']);
            //         break;
            //     case 'draft':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'draft']);
            //         break;
            //     case 'delete':
            //         Course::whereIn('id', $courseIds)->delete();
            //         break;
            // }

            $actionText = match($action) {
                'publish' => 'published',
                'draft' => 'moved to draft',
                'delete' => 'deleted',
            };

            return redirect()->route('admin.courses.index')
                ->with('success', "{$count} courses {$actionText} successfully!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error performing bulk action: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class VisitorsController extends Controller
{
    public function courses()
    {
        return view('courses');
    }
    public function about()
    {
        return view('aboutUs');
    }
       public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function add_category(){
        return view('admin.categories.add_category');
    }

    public function store_category(Request $request){
        // Redirect to the proper CMS CategoryController for handling
        // This ensures we use the existing, robust category management system
        $cmsController = new \App\Http\Controllers\Admin\CMS\CategoryController();

        try {
            // Use the existing CMS store method which has proper validation and database handling
            $result = $cmsController->store($request);

            // If successful, redirect back with success message
            return redirect()->route('add.category')->with('success', 'Category created successfully!');
        } catch (\Exception $e) {
            // If there's an error, redirect back with error message
            return redirect()->route('add.category')->with('error', 'Error creating category: ' . $e->getMessage());
        }
    }

    public function all_categories(){
        return view('admin.categories.all_categories');
    }
}

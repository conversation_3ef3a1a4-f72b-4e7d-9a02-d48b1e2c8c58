<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class VisitorsController extends Controller
{
    public function courses()
    {
        return view('courses');
    }
    public function about()
    {
        return view('aboutUs');
    }
       public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    public function add_category(){
        return view('admin.categories.add_category');
    }
    public function all_categories(){
        return view('admin.categories.all_categories');
    }
}

<!doctype html>
<html lang="en">

<head>

    <meta charset="utf-8" />
    <title>Add Category | Lernovate - Admin Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Bootstrap Css -->
    <link href="assets/css/bootstrap.min.css" id="bootstrap-style" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="assets/css/app.min.css" id="app-style" rel="stylesheet" type="text/css" />

    <!-- Custom Category Form Styles -->
    <style>
        .upload-config-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .category-guidelines .list-unstyled li {
            padding: 4px 0;
            font-size: 0.9rem;
        }

        .icon-preview {
            font-size: 1.2rem;
            color: #6c757d;
        }

        .file-type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .alert-info {
            border-left: 4px solid #17a2b8;
        }

        .card-title i {
            color: #007bff;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #6c757d;
        }

        .upload-section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }

        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .btn-group-custom .btn {
            margin-right: 8px;
        }

        .btn-group-custom .btn:last-child {
            margin-right: 0;
        }
    </style>

</head>


<body>

    <!-- <body data-layout="horizontal" data-topbar="colored"> -->

    <!-- Begin page -->
    <div id="layout-wrapper">


        @include('admindashboard.header')
        @include('admindashboard.verticalmenu')



        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">

            <div class="page-content">
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                <h4 class="mb-0">Add Category</h4>

                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                        <li class="breadcrumb-item"><a href="{{ route('all.categories') }}">Categories</a></li>
                                        <li class="breadcrumb-item active">Add Category</li>
                                    </ol>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row">
                        <div class="col-xl-8">
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title">Category Information</h4>
                                    <p class="card-title-desc">Fill in the category details and upload configuration settings.</p>

                                    @if(session('success'))
                                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    @endif

                                    @if($errors->any())
                                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <ul class="mb-0">
                                                @foreach($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    @endif

                                    <form class="needs-validation" novalidate action="{{ route('store.category') }}" method="POST">
                                        @csrf

                                        <!-- Basic Category Information -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="category-name">Category Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="category-name" name="name"
                                                        placeholder="Enter category name" required>
                                                    <div class="invalid-feedback">
                                                        Please provide a valid category name.
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="category-slug">Slug</label>
                                                    <input type="text" class="form-control" id="category-slug" name="slug"
                                                        placeholder="Auto-generated from name">
                                                    <div class="form-text">Leave empty to auto-generate from category name.</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="parent-category">Parent Category</label>
                                                    <select class="form-select" id="parent-category" name="parent_id">
                                                        <option value="">Select Parent Category (Optional)</option>
                                                        <!-- Example hierarchical structure -->
                                                        <option value="1">Technology</option>
                                                        <option value="2">&nbsp;&nbsp;├── Programming</option>
                                                        <option value="3">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;├── Web Development</option>
                                                        <option value="4">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;├── Mobile Development</option>
                                                        <option value="5">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;└── Desktop Development</option>
                                                        <option value="6">&nbsp;&nbsp;├── Database</option>
                                                        <option value="7">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;├── SQL Databases</option>
                                                        <option value="8">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;└── NoSQL Databases</option>
                                                        <option value="9">&nbsp;&nbsp;└── DevOps</option>
                                                        <option value="10">Design</option>
                                                        <option value="11">&nbsp;&nbsp;├── UI/UX Design</option>
                                                        <option value="12">&nbsp;&nbsp;├── Graphic Design</option>
                                                        <option value="13">&nbsp;&nbsp;└── Motion Graphics</option>
                                                        <option value="14">Business</option>
                                                        <option value="15">&nbsp;&nbsp;├── Marketing</option>
                                                        <option value="16">&nbsp;&nbsp;├── Finance</option>
                                                        <option value="17">&nbsp;&nbsp;└── Management</option>
                                                        <option value="18">Science</option>
                                                        <option value="19">&nbsp;&nbsp;├── Mathematics</option>
                                                        <option value="20">&nbsp;&nbsp;├── Physics</option>
                                                        <option value="21">&nbsp;&nbsp;└── Chemistry</option>
                                                    </select>
                                                    <div class="form-text">Choose a parent category to create a subcategory. Leave empty for top-level category.</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="category-color">Category Color</label>
                                                    <input type="color" class="form-control form-control-color" id="category-color"
                                                        name="color" value="#6777ef" title="Choose category color">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="category-icon">Icon Class</label>
                                                    <input type="text" class="form-control" id="category-icon" name="icon"
                                                        placeholder="e.g., fas fa-book">
                                                    <div class="form-text">FontAwesome icon class (optional).</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label" for="sort-order">Sort Order</label>
                                                    <input type="number" class="form-control" id="sort-order" name="sort_order"
                                                        value="0" min="0">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label" for="category-description">Description</label>
                                            <textarea class="form-control" id="category-description" name="description"
                                                rows="3" placeholder="Enter category description (optional)"></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                                                <label class="form-check-label" for="is-active">Active Category</label>
                                            </div>
                                        </div>

                                        <!-- File Upload Configuration Section -->
                                        <hr class="my-4">
                                        <h5 class="mb-3">
                                            <i class="fas fa-upload me-2"></i>File Upload Configuration
                                        </h5>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enable-uploads" name="enable_uploads">
                                                <label class="form-check-label" for="enable-uploads">Enable File Uploads for this Category</label>
                                            </div>
                                            <div class="form-text">Allow users to upload files when creating content in this category.</div>
                                        </div>

                                        <div id="upload-config-section" class="upload-config-section" style="display: none;">
                                            <div class="upload-section-header">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-cog me-2"></i>Upload Configuration Settings
                                                </h6>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label" for="max-file-size">Maximum File Size (MB)</label>
                                                        <input type="number" class="form-control" id="max-file-size" name="max_file_size"
                                                            value="10" min="1" max="100" step="0.1">
                                                        <div class="form-text">Maximum file size allowed for uploads (in megabytes).</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label" for="max-files">Maximum Number of Files</label>
                                                        <input type="number" class="form-control" id="max-files" name="max_files"
                                                            value="5" min="1" max="20">
                                                        <div class="form-text">Maximum number of files that can be uploaded per item.</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label" for="allowed-file-types">Allowed File Types</label>
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="type-images" name="allowed_types[]" value="images">
                                                            <label class="form-check-label" for="type-images">
                                                                <i class="fas fa-image me-1"></i>Images (jpg, png, gif, webp)
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="type-documents" name="allowed_types[]" value="documents">
                                                            <label class="form-check-label" for="type-documents">
                                                                <i class="fas fa-file-alt me-1"></i>Documents (pdf, doc, docx)
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="type-videos" name="allowed_types[]" value="videos">
                                                            <label class="form-check-label" for="type-videos">
                                                                <i class="fas fa-video me-1"></i>Videos (mp4, avi, mov)
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="type-audio" name="allowed_types[]" value="audio">
                                                            <label class="form-check-label" for="type-audio">
                                                                <i class="fas fa-music me-1"></i>Audio (mp3, wav, ogg)
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label" for="custom-extensions">Custom File Extensions</label>
                                                <input type="text" class="form-control" id="custom-extensions" name="custom_extensions"
                                                    placeholder="e.g., zip, rar, 7z (comma separated)">
                                                <div class="form-text">Additional file extensions to allow (comma separated, without dots).</div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label" for="upload-directory">Upload Directory Path</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">storage/uploads/</span>
                                                    <input type="text" class="form-control" id="upload-directory" name="upload_directory"
                                                        placeholder="categories/category-name" value="categories">
                                                    <div class="invalid-feedback">
                                                        Please provide a valid directory path.
                                                    </div>
                                                </div>
                                                <div class="form-text">Directory where files will be stored (relative to storage/uploads/).</div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="require-approval" name="require_approval">
                                                            <label class="form-check-label" for="require-approval">
                                                                Require Admin Approval for Uploads
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="scan-viruses" name="scan_viruses" checked>
                                                            <label class="form-check-label" for="scan-viruses">
                                                                Enable Virus Scanning
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Form Actions -->
                                        <hr class="my-4">
                                        <div class="d-flex justify-content-between">
                                            <a href="{{ route('all.categories') }}" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-1"></i>Back to Categories
                                            </a>
                                            <div>
                                                <button type="reset" class="btn btn-outline-secondary me-2">
                                                    <i class="fas fa-undo me-1"></i>Reset Form
                                                </button>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i>Create Category
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <!-- end card -->
                        </div> <!-- end col -->

                        <div class="col-xl-4">
                            <div class="card">
                                <div class="card-body category-guidelines">
                                    <h4 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>Category Guidelines
                                    </h4>
                                    <div class="mb-3">
                                        <h6 class="text-primary">Category Naming</h6>
                                        <ul class="list-unstyled mb-3">
                                            <li><i class="fas fa-check text-success me-2"></i>Use clear, descriptive names</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Keep names concise (2-3 words max)</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Use title case formatting</li>
                                        </ul>
                                    </div>

                                    <div class="mb-3">
                                        <h6 class="text-primary">File Upload Best Practices</h6>
                                        <ul class="list-unstyled mb-3">
                                            <li><i class="fas fa-shield-alt text-info me-2"></i>Enable virus scanning for security</li>
                                            <li><i class="fas fa-folder text-warning me-2"></i>Use organized directory structure</li>
                                            <li><i class="fas fa-weight-hanging text-danger me-2"></i>Set reasonable file size limits</li>
                                            <li><i class="fas fa-file-check text-success me-2"></i>Restrict file types as needed</li>
                                        </ul>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-lightbulb me-1"></i>Pro Tip
                                        </h6>
                                        <p class="mb-0">Categories with file uploads enabled will show upload options to users when creating content. Configure these settings carefully based on your content requirements.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title">
                                        <i class="fas fa-cogs me-2"></i>Quick Actions
                                    </h4>
                                    <div class="d-grid gap-2">
                                        <a href="{{ route('all.categories') }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-list me-1"></i>View All Categories
                                        </a>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="document.getElementById('category-name').focus()">
                                            <i class="fas fa-edit me-1"></i>Focus Name Field
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleUploadConfig()">
                                            <i class="fas fa-upload me-1"></i>Toggle Upload Config
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div>
                    <!-- end row -->

                </div> <!-- container-fluid -->
            </div>
            <!-- End Page-content -->


            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-6">
                            <script>
                                document.write(new Date().getFullYear())
                            </script> © Minible.
                        </div>
                        <div class="col-sm-6">
                            <div class="text-sm-end d-none d-sm-block">
                                Crafted with <i class="mdi mdi-heart text-danger"></i> by <a
                                    href="https://themesbrand.com/" target="_blank" class="text-reset">Themesbrand</a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->



    <!-- Right Sidebar -->
    <div class="right-bar">
        <div data-simplebar class="h-100">
            <div class="rightbar-title d-flex align-items-center p-3">

                <h5 class="m-0 me-2">Settings</h5>

                <a href="javascript:void(0);" class="right-bar-toggle ms-auto">
                    <i class="mdi mdi-close noti-icon"></i>
                </a>
            </div>

            <!-- Settings -->
            <hr class="m-0" />

            <div class="p-4">
                <h6 class="mb-3">Layout</h6>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout" id="layout-vertical" value="vertical">
                    <label class="form-check-label" for="layout-vertical">Vertical</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout" id="layout-horizontal"
                        value="horizontal">
                    <label class="form-check-label" for="layout-horizontal">Horizontal</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2">Layout Mode</h6>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-mode" id="layout-mode-light"
                        value="light">
                    <label class="form-check-label" for="layout-mode-light">Light</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-mode" id="layout-mode-dark" value="dark">
                    <label class="form-check-label" for="layout-mode-dark">Dark</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2">Layout Width</h6>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-width" id="layout-width-fuild"
                        value="fuild" onchange="document.body.setAttribute('data-layout-size', 'fluid')">
                    <label class="form-check-label" for="layout-width-fuild">Fluid</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-width" id="layout-width-boxed"
                        value="boxed" onchange="document.body.setAttribute('data-layout-size', 'boxed')">
                    <label class="form-check-label" for="layout-width-boxed">Boxed</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2">Topbar Color</h6>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="topbar-color" id="topbar-color-light"
                        value="light" onchange="document.body.setAttribute('data-topbar', 'light')">
                    <label class="form-check-label" for="topbar-color-light">Light</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="topbar-color" id="topbar-color-dark" value="dark"
                        onchange="document.body.setAttribute('data-topbar', 'dark')">
                    <label class="form-check-label" for="topbar-color-dark">Dark</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2 sidebar-setting">Sidebar Size</h6>

                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-size" id="sidebar-size-default"
                        value="default" onchange="document.body.setAttribute('data-sidebar-size', 'lg')">
                    <label class="form-check-label" for="sidebar-size-default">Default</label>
                </div>
                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-size" id="sidebar-size-compact"
                        value="compact" onchange="document.body.setAttribute('data-sidebar-size', 'small')">
                    <label class="form-check-label" for="sidebar-size-compact">Compact</label>
                </div>
                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-size" id="sidebar-size-small"
                        value="small" onchange="document.body.setAttribute('data-sidebar-size', 'sm')">
                    <label class="form-check-label" for="sidebar-size-small">Small (Icon View)</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2 sidebar-setting">Sidebar Color</h6>

                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-color" id="sidebar-color-light"
                        value="light" onchange="document.body.setAttribute('data-sidebar', 'light')">
                    <label class="form-check-label" for="sidebar-color-light">Light</label>
                </div>
                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-color" id="sidebar-color-dark"
                        value="dark" onchange="document.body.setAttribute('data-sidebar', 'dark')">
                    <label class="form-check-label" for="sidebar-color-dark">Dark</label>
                </div>
                <div class="form-check sidebar-setting">
                    <input class="form-check-input" type="radio" name="sidebar-color" id="sidebar-color-colored"
                        value="colored" onchange="document.body.setAttribute('data-sidebar', 'colored')">
                    <label class="form-check-label" for="sidebar-color-colored">Colored</label>
                </div>

                <h6 class="mt-4 mb-3 pt-2">Direction</h6>

                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-direction" id="layout-direction-ltr"
                        value="ltr">
                    <label class="form-check-label" for="layout-direction-ltr">LTR</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="layout-direction" id="layout-direction-rtl"
                        value="rtl">
                    <label class="form-check-label" for="layout-direction-rtl">RTL</label>
                </div>

            </div>

        </div> <!-- end slimscroll-menu-->
    </div>
    <!-- /Right-bar -->

    <!-- Right bar overlay-->
    <div class="rightbar-overlay"></div>

    <!-- JAVASCRIPT -->
    <script src="assets/libs/jquery/jquery.min.js"></script>
    <script src="assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/libs/metismenu/metisMenu.min.js"></script>
    <script src="assets/libs/simplebar/simplebar.min.js"></script>
    <script src="assets/libs/node-waves/waves.min.js"></script>
    <script src="assets/libs/waypoints/lib/jquery.waypoints.min.js"></script>
    <script src="assets/libs/jquery.counterup/jquery.counterup.min.js"></script>

    <!-- parsleyjs -->
    <script src="assets/libs/parsleyjs/parsley.min.js"></script>

    <script src="assets/js/pages/form-validation.init.js"></script>

    <!-- App js -->
    <script src="assets/js/app.js"></script>

    <!-- Category Form Custom Scripts -->
    <script>
        $(document).ready(function() {
            // Auto-generate slug from category name
            $('#category-name').on('keyup', function() {
                var name = $(this).val();
                var slug = name.toLowerCase()
                    .replace(/[^\w ]+/g, '')
                    .replace(/ +/g, '-');
                $('#category-slug').val(slug);
            });

            // Toggle upload configuration section
            $('#enable-uploads').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#upload-config-section').slideDown();
                } else {
                    $('#upload-config-section').slideUp();
                }
            });

            // Update upload directory based on category name
            $('#category-name').on('keyup', function() {
                var name = $(this).val();
                var directory = 'categories/' + name.toLowerCase()
                    .replace(/[^\w ]+/g, '')
                    .replace(/ +/g, '-');
                $('#upload-directory').val(directory);
            });

            // Form validation
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });

            // File type selection helper
            $('.form-check-input[name="allowed_types[]"]').on('change', function() {
                var selectedTypes = [];
                $('.form-check-input[name="allowed_types[]"]:checked').each(function() {
                    selectedTypes.push($(this).val());
                });

                if (selectedTypes.length === 0) {
                    $('#custom-extensions').prop('required', true);
                    $('#custom-extensions').attr('placeholder', 'Required: Enter file extensions (e.g., zip, rar, 7z)');
                } else {
                    $('#custom-extensions').prop('required', false);
                    $('#custom-extensions').attr('placeholder', 'e.g., zip, rar, 7z (comma separated)');
                }
            });
        });

        // Function for quick action button
        function toggleUploadConfig() {
            $('#enable-uploads').trigger('click');
        }

        // Color picker preview
        $('#category-color').on('change', function() {
            var color = $(this).val();
            $(this).css('border-color', color);
        });

        // Icon preview functionality
        $('#category-icon').on('keyup', function() {
            var iconClass = $(this).val();
            var preview = $('#icon-preview');

            if (preview.length === 0) {
                $(this).after('<span id="icon-preview" class="ms-2"></span>');
                preview = $('#icon-preview');
            }

            if (iconClass) {
                preview.html('<i class="' + iconClass + '"></i>');
            } else {
                preview.html('');
            }
        });

        // File size validation
        $('#max-file-size').on('change', function() {
            var size = parseFloat($(this).val());
            if (size > 100) {
                $(this).val(100);
                alert('Maximum file size cannot exceed 100MB');
            } else if (size < 0.1) {
                $(this).val(0.1);
                alert('Minimum file size must be at least 0.1MB');
            }
        });

        // Directory path validation
        $('#upload-directory').on('keyup', function() {
            var path = $(this).val();
            // Remove invalid characters
            var cleanPath = path.replace(/[^a-zA-Z0-9\-_\/]/g, '');
            if (cleanPath !== path) {
                $(this).val(cleanPath);
            }
        });

        // Form submission confirmation
        $('.needs-validation').on('submit', function(e) {
            var uploadsEnabled = $('#enable-uploads').is(':checked');
            var hasFileTypes = $('.form-check-input[name="allowed_types[]"]:checked').length > 0;
            var hasCustomExtensions = $('#custom-extensions').val().trim() !== '';

            if (uploadsEnabled && !hasFileTypes && !hasCustomExtensions) {
                e.preventDefault();
                alert('Please select at least one file type or specify custom extensions when uploads are enabled.');
                return false;
            }

            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // Focus on first invalid field
                var firstInvalid = $(this).find(':invalid').first();
                if (firstInvalid.length) {
                    firstInvalid.focus();

                    // Scroll to the field if it's not visible
                    $('html, body').animate({
                        scrollTop: firstInvalid.offset().top - 100
                    }, 500);
                }
            }

            $(this).addClass('was-validated');
        });
    </script>

</body>

</html>

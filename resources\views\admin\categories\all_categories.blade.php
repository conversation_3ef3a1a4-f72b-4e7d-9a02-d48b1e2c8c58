<!doctype html>
<html lang="en">

<head>

        <meta charset="utf-8" />
        <title>All Categories | Lernovate - Admin Dashboard</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
        <meta content="Themesbrand" name="author" />
        <!-- App favicon -->
        <link rel="shortcut icon" href="assets/images/favicon.ico">

        <!-- Bootstrap Css -->
        <link href="assets/css/bootstrap.min.css" id="bootstrap-style" rel="stylesheet" type="text/css" />
        <!-- Icons Css -->
        <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />
        <!-- App Css-->
        <link href="assets/css/app.min.css" id="app-style" rel="stylesheet" type="text/css" />

        <!-- Custom Categories Table Styles -->
        <style>
            .category-color-badge {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: inline-block;
                border: 2px solid #fff;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            }

            .category-icon {
                font-size: 1.2rem;
                color: #6c757d;
                width: 25px;
                text-align: center;
            }

            .category-hierarchy {
                font-family: monospace;
                color: #6c757d;
                margin-right: 8px;
            }

            .upload-status-enabled {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }

            .upload-status-disabled {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }

            .file-type-icons {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
            }

            .file-type-icon {
                font-size: 0.9rem;
                padding: 2px 4px;
                border-radius: 3px;
                background-color: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
            }

            .description-truncated {
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .table-actions {
                white-space: nowrap;
            }

            .btn-action {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
                margin-right: 0.25rem;
            }

            .search-filter-section {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .table-header-actions {
                display: flex;
                justify-content: between;
                align-items: center;
                margin-bottom: 20px;
            }

            .categories-table th {
                background-color: #f8f9fa;
                border-bottom: 2px solid #dee2e6;
                font-weight: 600;
                color: #495057;
                white-space: nowrap;
            }

            .categories-table td {
                vertical-align: middle;
                border-bottom: 1px solid #dee2e6;
            }

            .status-active {
                background-color: #d4edda;
                color: #155724;
                padding: 0.25rem 0.5rem;
                border-radius: 0.375rem;
                font-size: 0.75rem;
                font-weight: 500;
            }

            .status-inactive {
                background-color: #f8d7da;
                color: #721c24;
                padding: 0.25rem 0.5rem;
                border-radius: 0.375rem;
                font-size: 0.75rem;
                font-weight: 500;
            }
        </style>

    </head>

    
    <body>

    <!-- <body data-layout="horizontal" data-topbar="colored"> -->

        <!-- Begin page -->
        <div id="layout-wrapper">


            @include('admindashboard.header')
            @include('admindashboard.verticalmenu')




            

            <!-- ============================================================== -->
            <!-- Start right Content here -->
            <!-- ============================================================== -->
            <div class="main-content">

                <div class="page-content">
                    <div class="container-fluid">

                        <!-- start page title -->
                        <div class="row">
                            <div class="col-12">
                                <div class="page-title-box d-flex align-items-center justify-content-between">
                                    <h4 class="mb-0">Editable Table</h4>

                                    <div class="page-title-right">
                                        <ol class="breadcrumb m-0">
                                            <li class="breadcrumb-item"><a href="javascript: void(0);">Tables</a></li>
                                            <li class="breadcrumb-item active">Editable Table</li>
                                        </ol>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <!-- end page title -->
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
        
                                        <h4 class="card-title">Table Edits</h4>
                                        <p class="card-title-desc">Table Edits is a lightweight jQuery plugin for making table rows editable.</p>
        
                                        <div class="table-responsive">
                                            <table class="table table-editable table-nowrap align-middle table-edits">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>Name</th>
                                                        <th>Age</th>
                                                        <th>Gender</th>
                                                        <th>Edit</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Technology Category -->
                                                    <tr data-id="1">
                                                        <td>1</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="category-hierarchy"></span>
                                                                <strong>Technology</strong>
                                                            </div>
                                                        </td>
                                                        <td><code>technology</code></td>
                                                        <td><span class="text-muted">—</span></td>
                                                        <td>
                                                            <div class="description-truncated" title="All technology-related courses and content">
                                                                All technology-related courses and content
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="category-color-badge" style="background-color: #007bff;" title="#007bff"></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-laptop-code category-icon" title="fas fa-laptop-code"></i>
                                                        </td>
                                                        <td>
                                                            <span class="badge upload-status-enabled">Enabled</span>
                                                            <div class="file-type-icons mt-1">
                                                                <span class="file-type-icon" title="Images"><i class="fas fa-image"></i></span>
                                                                <span class="file-type-icon" title="Documents"><i class="fas fa-file-alt"></i></span>
                                                                <span class="file-type-icon" title="Videos"><i class="fas fa-video"></i></span>
                                                            </div>
                                                            <small class="text-muted d-block">Max: 50MB</small>
                                                        </td>
                                                        <td><span class="badge status-active">Active</span></td>
                                                        <td>1</td>
                                                        <td>
                                                            <small class="text-muted">Jan 15, 2024</small>
                                                        </td>
                                                        <td class="table-actions">
                                                            <a href="#" class="btn btn-outline-primary btn-action" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button class="btn btn-outline-danger btn-action" onclick="deleteCategory(1)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>

                                                    <!-- Programming Subcategory -->
                                                    <tr data-id="2">
                                                        <td>2</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="category-hierarchy">&nbsp;&nbsp;├──</span>
                                                                Programming
                                                            </div>
                                                        </td>
                                                        <td><code>programming</code></td>
                                                        <td>Technology</td>
                                                        <td>
                                                            <div class="description-truncated" title="Programming languages and development">
                                                                Programming languages and development
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="category-color-badge" style="background-color: #28a745;" title="#28a745"></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-code category-icon" title="fas fa-code"></i>
                                                        </td>
                                                        <td>
                                                            <span class="badge upload-status-enabled">Enabled</span>
                                                            <div class="file-type-icons mt-1">
                                                                <span class="file-type-icon" title="Documents"><i class="fas fa-file-alt"></i></span>
                                                                <span class="file-type-icon" title="Videos"><i class="fas fa-video"></i></span>
                                                            </div>
                                                            <small class="text-muted d-block">Max: 25MB</small>
                                                        </td>
                                                        <td><span class="badge status-active">Active</span></td>
                                                        <td>2</td>
                                                        <td>
                                                            <small class="text-muted">Jan 16, 2024</small>
                                                        </td>
                                                        <td class="table-actions">
                                                            <a href="#" class="btn btn-outline-primary btn-action" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button class="btn btn-outline-danger btn-action" onclick="deleteCategory(2)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>

                                                    <!-- Web Development Sub-subcategory -->
                                                    <tr data-id="3">
                                                        <td>3</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="category-hierarchy">&nbsp;&nbsp;│&nbsp;&nbsp;&nbsp;&nbsp;├──</span>
                                                                Web Development
                                                            </div>
                                                        </td>
                                                        <td><code>web-development</code></td>
                                                        <td>Programming</td>
                                                        <td>
                                                            <div class="description-truncated" title="Frontend and backend web development technologies">
                                                                Frontend and backend web development technologies
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="category-color-badge" style="background-color: #17a2b8;" title="#17a2b8"></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-globe category-icon" title="fas fa-globe"></i>
                                                        </td>
                                                        <td>
                                                            <span class="badge upload-status-enabled">Enabled</span>
                                                            <div class="file-type-icons mt-1">
                                                                <span class="file-type-icon" title="Images"><i class="fas fa-image"></i></span>
                                                                <span class="file-type-icon" title="Documents"><i class="fas fa-file-alt"></i></span>
                                                            </div>
                                                            <small class="text-muted d-block">Max: 15MB</small>
                                                        </td>
                                                        <td><span class="badge status-active">Active</span></td>
                                                        <td>3</td>
                                                        <td>
                                                            <small class="text-muted">Jan 17, 2024</small>
                                                        </td>
                                                        <td class="table-actions">
                                                            <a href="#" class="btn btn-outline-primary btn-action" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button class="btn btn-outline-danger btn-action" onclick="deleteCategory(3)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>

                                                    <!-- Design Category -->
                                                    <tr data-id="4">
                                                        <td>4</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="category-hierarchy"></span>
                                                                <strong>Design</strong>
                                                            </div>
                                                        </td>
                                                        <td><code>design</code></td>
                                                        <td><span class="text-muted">—</span></td>
                                                        <td>
                                                            <div class="description-truncated" title="Creative design and visual arts">
                                                                Creative design and visual arts
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="category-color-badge" style="background-color: #e83e8c;" title="#e83e8c"></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-palette category-icon" title="fas fa-palette"></i>
                                                        </td>
                                                        <td>
                                                            <span class="badge upload-status-enabled">Enabled</span>
                                                            <div class="file-type-icons mt-1">
                                                                <span class="file-type-icon" title="Images"><i class="fas fa-image"></i></span>
                                                                <span class="file-type-icon" title="Videos"><i class="fas fa-video"></i></span>
                                                            </div>
                                                            <small class="text-muted d-block">Max: 100MB</small>
                                                        </td>
                                                        <td><span class="badge status-active">Active</span></td>
                                                        <td>4</td>
                                                        <td>
                                                            <small class="text-muted">Jan 18, 2024</small>
                                                        </td>
                                                        <td class="table-actions">
                                                            <a href="#" class="btn btn-outline-primary btn-action" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button class="btn btn-outline-danger btn-action" onclick="deleteCategory(4)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>

                                                    <!-- Business Category (Inactive) -->
                                                    <tr data-id="5">
                                                        <td>5</td>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <span class="category-hierarchy"></span>
                                                                <strong>Business</strong>
                                                            </div>
                                                        </td>
                                                        <td><code>business</code></td>
                                                        <td><span class="text-muted">—</span></td>
                                                        <td>
                                                            <div class="description-truncated" title="Business and entrepreneurship courses">
                                                                Business and entrepreneurship courses
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="category-color-badge" style="background-color: #ffc107;" title="#ffc107"></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-briefcase category-icon" title="fas fa-briefcase"></i>
                                                        </td>
                                                        <td>
                                                            <span class="badge upload-status-disabled">Disabled</span>
                                                            <small class="text-muted d-block">No uploads</small>
                                                        </td>
                                                        <td><span class="badge status-inactive">Inactive</span></td>
                                                        <td>5</td>
                                                        <td>
                                                            <small class="text-muted">Jan 19, 2024</small>
                                                        </td>
                                                        <td class="table-actions">
                                                            <a href="#" class="btn btn-outline-primary btn-action" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button class="btn btn-outline-danger btn-action" onclick="deleteCategory(5)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                                </table>
                                        </div>

                                        <!-- Pagination and Info -->
                                        <div class="row align-items-center mt-3">
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted">Showing 1 to 5 of 5 entries</span>
                                                    <div class="ms-3">
                                                        <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                                                            <i class="fas fa-filter me-1"></i>Clear Filters
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <nav aria-label="Categories pagination">
                                                    <ul class="pagination pagination-sm justify-content-end mb-0">
                                                        <li class="page-item disabled">
                                                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                                                        </li>
                                                        <li class="page-item active">
                                                            <a class="page-link" href="#">1</a>
                                                        </li>
                                                        <li class="page-item disabled">
                                                            <a class="page-link" href="#">Next</a>
                                                        </li>
                                                    </ul>
                                                </nav>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div> <!-- end col -->
                        </div> <!-- end row -->
                    </div> <!-- container-fluid -->
                </div>
                <!-- End Page-content -->

                
                <footer class="footer">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-sm-6">
                                <script>document.write(new Date().getFullYear())</script> © Minible.
                            </div>
                            <div class="col-sm-6">
                                <div class="text-sm-end d-none d-sm-block">
                                    Crafted with <i class="mdi mdi-heart text-danger"></i> by <a href="https://themesbrand.com/" target="_blank" class="text-reset">Themesbrand</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
            <!-- end main content-->

        </div>
        <!-- END layout-wrapper -->

        

        <!-- Right Sidebar -->
        <div class="right-bar">
            <div data-simplebar class="h-100">
                <div class="rightbar-title d-flex align-items-center p-3">

                    <h5 class="m-0 me-2">Settings</h5>

                    <a href="javascript:void(0);" class="right-bar-toggle ms-auto">
                        <i class="mdi mdi-close noti-icon"></i>
                    </a>
                </div>

                <!-- Settings -->
                <hr class="m-0" />

                <div class="p-4">
                    <h6 class="mb-3">Layout</h6>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout"
                            id="layout-vertical" value="vertical">
                        <label class="form-check-label" for="layout-vertical">Vertical</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout"
                            id="layout-horizontal" value="horizontal">
                        <label class="form-check-label" for="layout-horizontal">Horizontal</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2">Layout Mode</h6>

                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-mode"
                            id="layout-mode-light" value="light">
                        <label class="form-check-label" for="layout-mode-light">Light</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-mode"
                            id="layout-mode-dark" value="dark">
                        <label class="form-check-label" for="layout-mode-dark">Dark</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2">Layout Width</h6>

                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-width"
                            id="layout-width-fuild" value="fuild" onchange="document.body.setAttribute('data-layout-size', 'fluid')">
                        <label class="form-check-label" for="layout-width-fuild">Fluid</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-width"
                            id="layout-width-boxed" value="boxed" onchange="document.body.setAttribute('data-layout-size', 'boxed')">
                        <label class="form-check-label" for="layout-width-boxed">Boxed</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2">Topbar Color</h6>

                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="topbar-color"
                            id="topbar-color-light" value="light" onchange="document.body.setAttribute('data-topbar', 'light')">
                        <label class="form-check-label" for="topbar-color-light">Light</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="topbar-color"
                            id="topbar-color-dark" value="dark" onchange="document.body.setAttribute('data-topbar', 'dark')">
                        <label class="form-check-label" for="topbar-color-dark">Dark</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2 sidebar-setting">Sidebar Size</h6>

                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-size"
                            id="sidebar-size-default" value="default" onchange="document.body.setAttribute('data-sidebar-size', 'lg')">
                        <label class="form-check-label" for="sidebar-size-default">Default</label>
                    </div>
                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-size"
                            id="sidebar-size-compact" value="compact" onchange="document.body.setAttribute('data-sidebar-size', 'small')">
                        <label class="form-check-label" for="sidebar-size-compact">Compact</label>
                    </div>
                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-size"
                            id="sidebar-size-small" value="small" onchange="document.body.setAttribute('data-sidebar-size', 'sm')">
                        <label class="form-check-label" for="sidebar-size-small">Small (Icon View)</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2 sidebar-setting">Sidebar Color</h6>

                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-color"
                            id="sidebar-color-light" value="light" onchange="document.body.setAttribute('data-sidebar', 'light')">
                        <label class="form-check-label" for="sidebar-color-light">Light</label>
                    </div>
                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-color"
                            id="sidebar-color-dark" value="dark" onchange="document.body.setAttribute('data-sidebar', 'dark')">
                        <label class="form-check-label" for="sidebar-color-dark">Dark</label>
                    </div>
                    <div class="form-check sidebar-setting">
                        <input class="form-check-input" type="radio" name="sidebar-color"
                            id="sidebar-color-colored" value="colored" onchange="document.body.setAttribute('data-sidebar', 'colored')">
                        <label class="form-check-label" for="sidebar-color-colored">Colored</label>
                    </div>

                    <h6 class="mt-4 mb-3 pt-2">Direction</h6>

                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-direction"
                            id="layout-direction-ltr" value="ltr">
                        <label class="form-check-label" for="layout-direction-ltr">LTR</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="layout-direction"
                            id="layout-direction-rtl" value="rtl">
                        <label class="form-check-label" for="layout-direction-rtl">RTL</label>
                    </div>

                </div>

            </div> <!-- end slimscroll-menu-->
        </div>
        <!-- /Right-bar -->

        <!-- Right bar overlay-->
        <div class="rightbar-overlay"></div>

        <!-- JAVASCRIPT -->
        <script src="assets/libs/jquery/jquery.min.js"></script>
        <script src="assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
        <script src="assets/libs/metismenu/metisMenu.min.js"></script>
        <script src="assets/libs/simplebar/simplebar.min.js"></script>
        <script src="assets/libs/node-waves/waves.min.js"></script>
        <script src="assets/libs/waypoints/lib/jquery.waypoints.min.js"></script>
        <script src="assets/libs/jquery.counterup/jquery.counterup.min.js"></script>
        
        <!-- Table Editable plugin -->
        <script src="assets/libs/table-edits/build/table-edits.min.js"></script>

        <script src="assets/js/pages/table-editable.int.js"></script>

        <!-- App js -->
        <script src="assets/js/app.js"></script>

        <!-- Categories Table Custom Scripts -->
        <script>
            $(document).ready(function() {
                // Search functionality
                $('#categorySearch').on('keyup', function() {
                    var value = $(this).val().toLowerCase();
                    $('#categoriesTable tbody tr').filter(function() {
                        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                    });
                });

                // Status filter
                $('#statusFilter').on('change', function() {
                    var status = $(this).val();
                    if (status === '') {
                        $('#categoriesTable tbody tr').show();
                    } else {
                        $('#categoriesTable tbody tr').each(function() {
                            var rowStatus = $(this).find('.status-active, .status-inactive').hasClass('status-' + status);
                            $(this).toggle(rowStatus);
                        });
                    }
                });

                // Upload filter
                $('#uploadFilter').on('change', function() {
                    var uploadStatus = $(this).val();
                    if (uploadStatus === '') {
                        $('#categoriesTable tbody tr').show();
                    } else {
                        $('#categoriesTable tbody tr').each(function() {
                            var rowUploadStatus = $(this).find('.upload-status-enabled, .upload-status-disabled').hasClass('upload-status-' + uploadStatus);
                            $(this).toggle(rowUploadStatus);
                        });
                    }
                });

                // Tooltip initialization
                $('[title]').tooltip();
            });

            // Delete category function
            function deleteCategory(categoryId) {
                if (confirm('Are you sure you want to delete this category? This action cannot be undone and will also delete all subcategories.')) {
                    // Here you would typically make an AJAX call to delete the category
                    // For now, we'll just remove the row from the table
                    $('tr[data-id="' + categoryId + '"]').fadeOut(300, function() {
                        $(this).remove();
                    });

                    // Show success message
                    showNotification('Category deleted successfully!', 'success');
                }
            }

            // Show notification function
            function showNotification(message, type) {
                var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                var notification = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>';

                // Insert notification at the top of the page content
                $('.page-content .container-fluid').prepend(notification);

                // Auto-hide after 5 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            // Clear all filters
            function clearFilters() {
                $('#categorySearch').val('');
                $('#statusFilter').val('');
                $('#uploadFilter').val('');
                $('#categoriesTable tbody tr').show();
            }
        </script>

    </body>

<!-- Mirrored from themesbrand.com/minible/layouts/tables-editable.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 18:36:45 GMT -->
</html>
